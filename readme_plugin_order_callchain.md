# 工单插件调用链分析

本文档旨在分析工单(order)插件的完整代码调用链。

## 调用链

### 节点: (ctrl *OrderController) KsServerApply
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 作为提交“金山云服务器购买”工单的API入口点。它接收服务器申请的详细信息，构建一个工单数据对象，并调用业务逻辑层(Biz)来启动工单的创建流程。
- **输入参数**:
    - `ctx context.Context`: 请求上下文，用于传递请求范围内的信息，如日志、超时和取消信号。
    - `req *orderproto.KsServerApplyReq`: gRPC请求体，包含了创建服务器所需的所有参数，例如区域(Region)、镜像(Image)、硬件规格(HardwareSpec)、实例名(InstanceName)等。
- **输出说明**:
    - `resp *orderproto.KsServerApplyResp`: gRPC响应体，目前在代码中仅被初始化，未填充具体返回数据。
    - `err error`: 如果在处理过程中发生错误（如JSON序列化失败），则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as KsServerApply
    participant O as orderdto.Order
    participant B as ctrl.Biz

    C->>O: 根据req构建orderdto.Order对象
    Note right of C: 将服务器规格等信息序列化为JSON字符串并存入order.Info
    C->>B: 调用 ctrl.Biz.NewCreateOrder(ctx, order)
    Note right of C: 同步创建工单主体记录
    C->>B: 异步调用 ctrl.Biz.NewCreateStage(order)
    Note right of C: 在新的goroutine中为工单创建初始阶段(stage)
```

### 节点: (biz *OrderBiz) NewCreateOrder
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/order.go`
- **用途**: 负责创建新的工单记录。此函数从配置中获取工单类型的详细信息，准备一个数据库记录对象，并将其持久化到数据库中。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `order *orderdto.Order`: 包含工单核心信息的DTO（数据传输对象），如工单ID、类型、申请详情（JSON格式）、紧急程度、申请消息、申请人及运维负责人邮箱。
- **输出说明**:
    - `err error`: 如果在获取配置或数据库操作中发生错误，则返回错误。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B{获取工单配置};
    B --> C{构建数据库实体 orderdao.TbOrder};
    C --> D{调用 biz.Dao.OrderMysql.NewOrder 写入数据库};
    D --> E[结束];
```


### 节点: (dao *OrderMysqlDao) NewOrder
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/def_order.go` (接口定义), `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/zz_generated.def_order.go` (自动生成的实现)
- **用途**: 将一个新的工单记录插入到数据库的`tb_order`表中。这是一个底层的数据访问对象(DAO)方法。
- **输入参数**:
    - `order TbOrder`: 一个`TbOrder`结构体，它直接映射到`tb_order`表的列，包含了工单的所有持久化字段。
- **输出说明**:
    - `int64`: 返回插入操作影响的行数。
    - `error`: 如果数据库`INSERT`操作失败，则返回错误。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B[执行INSERT SQL语句];
    B --> C[返回结果];
    C --> D[结束];
```


### 节点: (biz *OrderBiz) NewCreateStage
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/order.go`
- **用途**: 异步地为新创建的工单初始化其所有处理阶段（stages），并启动审批流程。
- **输入参数**:
    - `order *orderdto.Order`: 包含工单核心信息的DTO。
- **输出说明**:
    - `error`: 在处理过程中（如获取配置、获取审批人、初始化阶段、发送飞书消息等）发生的任何错误都将被捕获并记录，同时会更新工单状态为失败。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B{获取工单配置};
    B --> C{获取审批人信息};
    C --> D{初始化所有工单阶段};
    D --> E{向第一个审批人发送飞书卡片};
    E --> F[结束];
```


### 节点: (biz *OrderBiz) InitOrderStages
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/order.go`
- **用途**: 根据工单类型的配置，在数据库中为工单创建所有预定义的阶段记录。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `orderID string`: 工单的唯一标识符。
    - `orderTypeConf hook.OrderTypeConf`: 包含工单阶段定义的配置对象。
    - `auditPersonInfoMap map[string]orderdto.PersonInfo`: 一个映射，包含了各审批模式对应的审批人信息。
- **输出说明**:
    - `err error`: 如果在创建任何一个阶段时数据库操作失败，则返回错误。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B{遍历所有预定义阶段};
    B --> C{为每个阶段构建 orderdao.TbStage 实体};
    C --> D{查找并设置阶段负责人(operator)};
    D --> E{调用 biz.Dao.StageMysql.NewStage 写入数据库};
    E --> B;
    B --> F[结束];
```


### 节点: (dao *StageMysqlDao) NewStage
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/def_stage.go` (接口定义), `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/zz_generated.def_stage.go` (自动生成的实现)
- **用途**: 将一个新的工单阶段记录插入到数据库的`tb_stage`表中。这是一个底层的数据访问对象(DAO)方法。
- **输入参数**:
    - `s TbStage`: 一个`TbStage`结构体，它直接映射到`tb_stage`表的列，包含了工单阶段的所有持久化字段。
- **输出说明**:
    - `int64`: 返回插入操作影响的行数。
    - `error`: 如果数据库`INSERT`操作失败，则返回错误。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B[执行INSERT SQL语句];
    B --> C[返回结果];
    C --> D[结束];
```


### 节点: (biz *OrderBiz) NewSendFeiShuAudit
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/order.go`
- **用途**: 负责向相关人员发送飞书审批卡片，以启动和推进工单流程。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `order *orderdto.Order`: 包含工单核心信息的DTO。
    - `auditPersonInfoMap map[string]orderdto.PersonInfo`: 一个映射，包含了各审批模式对应的审批人信息。
- **输出说明**:
    - `err error`: 如果在获取配置、构建卡片、发送消息或更新数据库时发生错误，则返回错误。
- **实现流程**:

```mermaid
sequenceDiagram
    participant A as NewSendFeiShuAudit
    participant D as biz.Dao.StageMysql
    participant F as feishuservice.Feishu

    A->>D: GetStages(ctx, order.OrderID)
    A->>A: 构建审批人和状态卡片元素
    A->>F: FeiShuAudit(ctx, openID, card)
    Note right of A: 向第一个审批人发送审批卡片
    A->>D: UpdateStageInfo(ctx, orderID, stageNum, stageInfo)
    A->>F: FeiShuAudit(ctx, openID, card)
    Note right of A: 向最后一个确认人发送通知卡片
    A->>D: UpdateStageInfo(ctx, orderID, stageNum, stageInfo)
```


### 节点: (ctrl *OrderController) GetMyDoneOrder
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 获取当前用户已完结的工单列表，支持分页查询。
- **输入参数**:
    - `ctx context.Context`: 请求上下文，用于获取用户信息。
    - `req *orderproto.GetMyDoneOrderReq`: gRPC请求体，包含分页参数 `Page` 和 `PageSize`。
- **输出说明**:
    - `resp *orderproto.GetMyDoneOrderResp`: gRPC响应体，包含工单总数 `Total` 和工单信息列表 `Orders`。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as GetMyDoneOrder
    participant U as ctxutil
    participant B as ctrl.Biz
    participant P as orderproto.Order

    C->>U: GetReqInfo(ctx)
    Note right of C: 获取用户邮箱
    C->>B: GetDoneOrdersByPage(ctx, userEmail, page, pageSize)
    B-->>C: 返回 totalNum, orderInfos, err
    C->>P: 遍历 orderInfos，转换为 proto.Order
    C-->>User: 返回 resp
```


### 节点: (biz *OrderBiz) GetDoneOrdersByPage
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/order.go`
- **用途**: 根据用户邮箱和分页参数，从数据库中检索已完结的工单列表。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `userEmail string`: 申请人的邮箱地址。
    - `page int64`: 当前页码。
    - `pageSize int64`: 每页的记录数。
- **输出说明**:
    - `totalNum int64`: 符合条件的总工单数。
    - `orderInfos []orderdto.OrderInfo`: 工单信息的切片。
    - `err error`: 如果数据库查询失败，则返回错误。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B{调用DAO获取总数};
    B --> C{计算分页偏移量};
    C --> D{调用DAO获取分页数据};
    D --> E{遍历结果并填充额外信息};
    E --> F[返回结果];
```


### 节点: (dao *OrderMysqlDao) GetDoneOrdersCountByProposerEmail
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/def_order.go` (接口定义), `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/zz_generated.def_order.go` (自动生成的实现)
- **用途**: 统计指定用户已完结的工单总数。
- **输入参数**:
    - `proposerEmail string`: 申请人的邮箱地址。
- **输出说明**:
    - `*SelectCount`: 包含总数的结果。
    - `error`: 如果数据库查询失败，则返回错误。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B[执行SELECT COUNT(*) SQL语句];
    B --> C[返回结果];
    C --> D[结束];
```

### 节点: (dao *OrderMysqlDao) GetDoneOrdersByProposerEmail
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/def_order.go` (接口定义), `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/zz_generated.def_order.go` (自动生成的实现)
- **用途**: 查询指定用户已完结的工单列表（分页）。
- **输入参数**:
    - `proposerEmail string`: 申请人的邮箱地址。
    - `offset int64`: 分页查询的起始位置。
    - `rows int64`: 查询的记录数。
- **输出说明**:
    - `[]TbOrder`: 工单列表。
    - `error`: 如果数据库查询失败，则返回错误。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B[执行SELECT SQL语句];
    B --> C[返回结果];
    C --> D[结束];
```


### 节点: (ctrl *OrderController) GetMyDoingOrder
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 获取当前用户正在进行中的工单列表，支持分页查询。
- **输入参数**:
    - `ctx context.Context`: 请求上下文，用于获取用户信息。
    - `req *orderproto.GetMyDoingOrderReq`: gRPC请求体，包含分页参数 `Page` 和 `PageSize`。
- **输出说明**:
    - `resp *orderproto.GetMyDoingOrderResp`: gRPC响应体，包含工单总数 `Total` 和工单信息列表 `Orders`。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as GetMyDoingOrder
    participant U as ctxutil
    participant B as ctrl.Biz
    participant P as orderproto.Order

    C->>U: GetReqInfo(ctx)
    Note right of C: 获取用户邮箱
    C->>B: GetDoingOrdersByPage(ctx, userEmail, page, pageSize)
    B-->>C: 返回 totalNum, orderInfos, err
    C->>P: 遍历 orderInfos，转换为 proto.Order
    C-->>User: 返回 resp
```


### 节点: (biz *OrderBiz) GetDoingOrdersByPage
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/order.go`
- **用途**: 根据用户邮箱和分页参数，从数据库中检索正在进行中的工单列表。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `userEmail string`: 申请人的邮箱地址。
    - `page int64`: 当前页码。
    - `pageSize int64`: 每页的记录数。
- **输出说明**:
    - `totalNum int64`: 符合条件的总工单数。
    - `orderInfos []orderdto.OrderInfo`: 工单信息的切片。
    - `err error`: 如果数据库查询失败，则返回错误。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B{调用DAO获取总数};
    B --> C{计算分页偏移量};
    C --> D{调用DAO获取分页数据};
    D --> E{遍历结果并填充额外信息};
    E --> F[返回结果];
```


### 节点: (dao *OrderMysqlDao) GetDoingOrdersCountByProposerEmail
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/def_order.go` (接口定义), `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/zz_generated.def_order.go` (自动生成的实现)
- **用途**: 统计指定用户正在进行中的工单总数。
- **输入参数**:
    - `proposerEmail string`: 申请人的邮箱地址。
- **输出说明**:
    - `*SelectCount`: 包含总数的结果。
    - `error`: 如果数据库查询失败，则返回错误。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B[执行SELECT COUNT(*) SQL语句];
    B --> C[返回结果];
    C --> D[结束];
```

### 节点: (dao *OrderMysqlDao) GetDoingOrdersByProposerEmail
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/def_order.go` (接口定义), `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/zz_generated.def_order.go` (自动生成的实现)
- **用途**: 查询指定用户正在进行中的工单列表（分页），并左连接`tb_stage`表获取当前阶段信息。
- **输入参数**:
    - `proposerEmail string`: 申请人的邮箱地址。
    - `offset int64`: 分页查询的起始位置。
    - `rows int64`: 查询的记录数。
- **输出说明**:
    - `[]TbCurrentOrderStageInfo`: 包含工单和当前阶段信息的列表。
    - `error`: 如果数据库查询失败，则返回错误。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B[执行SELECT ... LEFT JOIN SQL语句];
    B --> C[返回结果];
    C --> D[结束];
```


### 节点: (ctrl *OrderController) GetOrderDetail
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 获取指定工单的详细信息，包括工单主体信息和所有阶段的信息。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.GetOrderDetailReq`: gRPC请求体，包含要查询的 `OrderId`。
- **输出说明**:
    - `resp *orderproto.GetOrderDetailResp`: gRPC响应体，包含工单信息 `OrderInfo` 和阶段信息列表 `StageInfos`。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as GetOrderDetail
    participant B as ctrl.Biz
    participant P as orderproto

    C->>B: GetOrderDetail(ctx, req.OrderId)
    B-->>C: 返回 orderInfo, stageInfos, err
    C->>P: 转换 orderInfo 为 proto.Order
    C->>P: 遍历 stageInfos，转换为 proto.Stage
    C-->>User: 返回 resp
```


### 节点: (biz *OrderBiz) GetOrderDetail
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/order.go`
- **用途**: 从数据库中检索指定工单的详细信息，包括其所有阶段的记录。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `orderID string`: 要查询的工单ID。
- **输出说明**:
    - `orderInfo orderdto.OrderInfo`: 工单的详细信息。
    - `stageRecords []orderdto.StageRecord`: 该工单所有阶段的记录列表。
    - `err error`: 如果数据库查询失败，则返回错误。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B{调用DAO获取工单信息};
    B --> C{调用DAO获取所有阶段信息};
    C --> D{遍历阶段结果并填充额外信息};
    D --> E[返回结果];
```


### 节点: (dao *OrderMysqlDao) GetOrder
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/def_order.go` (接口定义), `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/zz_generated.def_order.go` (自动生成的实现)
- **用途**: 根据工单ID查询单个工单的完整信息。
- **输入参数**:
    - `orderID string`: 工单的唯一标识符。
- **输出说明**:
    - `*TbOrder`: 指向工单信息结构体的指针。
    - `error`: 如果数据库查询失败，则返回错误。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B[执行SELECT * SQL语句];
    B --> C[返回结果];
    C --> D[结束];
```

### 节点: (dao *StageMysqlDao) GetStages
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/def_stage.go` (接口定义), `app/ops/bpm/plugins/order/orderbiz/internal/orderdao/zz_generated.def_stage.go` (自动生成的实现)
- **用途**: 根据工单ID查询其所有的阶段信息，并按阶段编号升序排序。
- **输入参数**:
    - `orderID string`: 工单的唯一标识符。
- **输出说明**:
    - `[]TbStage`: 工单阶段列表。
    - `error`: 如果数据库查询失败，则返回错误。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B[执行SELECT * ... ORDER BY SQL语句];
    B --> C[返回结果];
    C --> D[结束];
```


### 节点: (ctrl *OrderController) ServerJumpImpower
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 处理服务器跳板机账号授权的工单请求。它会根据申请的IP，查询CMDB获取服务器信息，然后根据服务器的负责人信息，将一个大的授权请求拆分成多个独立的工单，分别提交给不同的审批人。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.ServerJumpImpowerReq`: gRPC请求体，包含授权类型、天数、服务器IP列表和申请理由。
- **输出说明**:
    - `resp *orderproto.ServerJumpImpowerResp`: gRPC响应体。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as ServerJumpImpower
    participant J as jumpserverbiz
    participant B as ctrl.Biz

    C->>J: GetInstance().GetInfoByCmdbIp(ctx, req.ServerIp)
    Note right of C: 查询CMDB获取服务器信息
    C->>C: 根据负责人拆分审批组 (approveMap)
    loop 每个审批组
        C->>B: NewCreateOrder(ctx, order)
        C->>B: go NewCreateStage(order)
    end
    C-->>User: 返回 resp
```


### 节点: (ctrl *OrderController) CdnDomainCreate
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 创建华为CDN域名的工单。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.CdnDomainCreateReq`: gRPC请求体，包含域名、业务类型、源站信息等。
- **输出说明**:
    - `resp *orderproto.CdnDomainCreateResp`: gRPC响应体。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as CdnDomainCreate
    participant O as orderdto.Order
    participant B as ctrl.Biz

    C->>O: 根据req构建orderdto.Order对象
    C->>B: 调用 ctrl.Biz.NewCreateOrder(ctx, order)
    C->>B: 异步调用 ctrl.Biz.NewCreateStage(order)
    C-->>User: 返回 resp
```


### 节点: (ctrl *OrderController) CommonOrder
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 一个通用的工单创建接口，可以处理所有类型的工单。它通过钩子函数（Hooks）来实现不同工单类型的特定逻辑，如参数校验、请求处理和工单分割。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.CommonOrderReq`: 通用的gRPC请求体，包含工单类型、标题、申请信息等。
- **输出说明**:
    - `resp *orderproto.CommonOrderResp`: gRPC响应体。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as CommonOrder
    participant Conf as config
    participant B as ctrl.Biz

    C->>Conf: 获取工单配置
    C->>Conf: 执行参数校验钩子
    C->>Conf: 执行请求处理钩子
    C->>Conf: 执行工单分割钩子
    loop 每个分割后的工单
        C->>B: NewCreateOrder(ctx, order)
        C->>B: go NewCreateStage(order)
    end
    C-->>User: 返回 resp
```


### 节点: (ctrl *OrderController) GetMyAuditOrder
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 获取当前用户需要审批的工单列表，支持多种搜索条件和分页。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.GetMyAuditOrderReq`: gRPC请求体，包含分页和搜索参数。
- **输出说明**:
    - `resp *orderproto.GetMyAuditOrderResp`: gRPC响应体，包含工单列表、可审批的工单类型和总数。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as GetMyAuditOrder
    participant B as ctrl.Biz
    participant P as orderproto.Order

    C->>C: 构建搜索参数 (searchParam)
    C->>B: GetMyAuditOrderWithSearch(ctx, searchParam)
    B-->>C: 返回 totalNum, orderInfos, err
    C->>B: GetAuditOrderTypesByOperator(ctx, userEmail)
    B-->>C: 返回 orderTypes, err
    C->>P: 遍历 orderInfos，转换为 proto.Order
    C-->>User: 返回 resp
```


### 节点: (biz *OrderBiz) GetMyAuditOrderWithSearch
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/order.go`
- **用途**: 根据动态搜索条件，查询指定用户需要审批的工单。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `param *orderdto.MyAuditOrderSearchParam`: 包含分页和所有搜索条件的参数对象。
- **输出说明**:
    - `totalNum int64`: 符合条件的总工单数。
    - `orderInfos []orderdto.OrderInfo`: 工单信息的切片。
    - `err error`: 如果数据库查询失败，则返回错误。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B{调用DAO执行动态查询};
    B --> C{遍历结果并填充额外信息};
    C --> D[返回结果];
```

### 节点: (biz *OrderBiz) GetAuditOrderTypesByOperator
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/order.go`
- **用途**: 获取一个用户作为审批人，所能审批的所有工单类型。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `operatorEmail string`: 审批人的邮箱地址。
- **输出说明**:
    - `orderTypes []string`: 工单类型的字符串列表。
    - `err error`: 如果数据库查询失败，则返回错误。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B{调用DAO查询工单类型};
    B --> C[返回结果];
```


### 节点: (ctrl *OrderController) DomainResolve
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 创建一个域名解析的工单。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.DomainResolveReq`: gRPC请求体，包含记录类型、域名、解析值等信息。
- **输出说明**:
    - `resp *orderproto.DomainResolveResp`: gRPC响应体。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as DomainResolve
    participant O as orderdto.Order
    participant B as ctrl.Biz

    C->>O: 根据req构建orderdto.Order对象
    C->>B: 调用 ctrl.Biz.NewCreateOrder(ctx, order)
    C->>B: 异步调用 ctrl.Biz.NewCreateStage(order)
    C-->>User: 返回 resp
```


### 节点: (ctrl *OrderController) OrderApproval
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 处理来自页面的工单审批操作（同意或拒绝）。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.OrderApprovalReq`: gRPC请求体，包含工单ID、阶段编号和审批动作。
- **输出说明**:
    - `resp *orderproto.OrderApprovalResp`: gRPC响应体。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as OrderApproval
    participant D as biz.Dao
    participant B as ctrl.Biz
    participant H as ctrl.FeishuHookBiz

    C->>D: GetStage(ctx, orderID, stageNum)
    Note right of C: 获取当前阶段信息并校验操作人
    C->>D: GetStages(ctx, orderID)
    C->>D: GetOrder(ctx, orderID)
    C->>B: GetPersonInfo(ctx, proposerEmail)
    C->>C: 构建审批结果卡片 (auditStatusDetails)
    C->>H: NewHandleMsgCardCallBack(param)
    Note right of C: 调用飞书钩子处理回调
    C-->>User: 返回 resp
```


### 节点: (biz *FeishuhookBiz) NewHandleMsgCardCallBack
- **所在代码文件相对路径**: `app/ops/bpm/plugins/feishuhook/feishuhookbiz/feishuhook.go`
- **用途**: 处理来自飞书的审批卡片回调。当用户在飞书上点击“同意”或“拒绝”后，此函数被调用以更新工单状态，并触发后续的流程。
- **输入参数**:
    - `param *feishuhookdto.AuditHookParam`: 包含回调所需的所有上下文信息，如工单ID、审批结果、当前阶段等。
- **输出说明**:
    - `err error`: 如果处理失败，则返回错误。
- **实现流程**:

```mermaid
sequenceDiagram
    participant H as NewHandleMsgCardCallBack
    participant B as orderbiz
    participant D as orderdao
    participant F as feishuservice

    H->>B: 获取工单和阶段信息
    H->>H: 根据审批结果更新卡片状态
    alt 审批通过
        H->>B: 触发下一阶段 (执行或下一个审批)
        B->>F: 发送下一阶段的飞书卡片
        B->>D: 更新数据库状态
    else 审批拒绝
        H->>B: 更新工单为拒绝状态
        B->>D: 更新数据库状态
        B->>F: 发送拒绝通知
    end
```


### 节点: (ctrl *OrderController) TurnFlowAudit
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 处理审批人流转的请求，即变更某个审批阶段的负责人。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.TurnFlowAuditReq`: gRPC请求体，包含工单ID、阶段编号和新的审批人邮箱。
- **输出说明**:
    - `resp *orderproto.TurnFlowAuditResp`: gRPC响应体，包含操作结果。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as TurnFlowAudit
    participant B as ctrl.Biz

    C->>B: TurnAuditOperator(ctx, orderID, newOperator, stageNum)
    B-->>C: 返回 stageAuditInfos, err
    C->>B: SendMsgCardForTurnFlowAudit(ctx, orderID, stageNum, newOperator, stageAuditInfos)
    B-->>C: 返回 err
    C-->>User: 返回 resp
```


### 节点: (biz *OrderBiz) TurnAuditOperator
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/order.go`
- **用途**: 执行变更审批人的核心逻辑，包括验证新审批人、更新数据库。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `orderID string`: 工单ID。
    - `newOperatorEmail string`: 新审批人的邮箱。
    - `stageNum int64`: 要变更的阶段编号。
- **输出说明**:
    - `stageAuditInfos []orderdto.StageInfo`: 所有阶段的审批信息。
    - `errback error`: 如果发生错误，则返回错误。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B{获取所有阶段信息};
    B --> C{校验阶段是否已完成};
    C --> D{验证新审批人是否存在};
    D --> E{更新数据库Stage表};
    E --> F{如果是运维审批阶段，更新Order表};
    F --> G[返回结果];
```

### 节点: (biz *OrderBiz) SendMsgCardForTurnFlowAudit
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/orderbiz/order.go`
- **用途**: 在审批人变更后，发送一系列飞书消息通知相关人员。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `orderID string`: 工单ID。
    - `stageNum int64`: 变更的阶段编号。
    - `newOperatorEmail string`: 新审批人的邮箱。
    - `stageAuditInfos []orderdto.StageInfo`: 所有阶段的审批信息。
- **输出说明**:
    - `error`: 如果发送消息失败，则返回错误。
- **实现流程**:

```mermaid
sequenceDiagram
    participant S as SendMsgCardForTurnFlowAudit
    participant M as MakeAuditExecMsgCard
    participant G as SendTurnAuditMsgCardAndGray
    participant A as SendAuditMsgCard

    S->>M: 构造消息卡片基础内容
    S->>G: 发送通知并置灰旧卡片
    S->>A: 向新审批人发送审批卡片
```


### 节点: (ctrl *OrderController) NewModelNode
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 创建一个新的模型节点工单。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.NewModelNodeReq`: gRPC请求体。
- **输出说明**:
    - `resp *orderproto.NewModelNodeResp`: gRPC响应体。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as NewModelNode
    participant O as orderdto.Order
    participant B as ctrl.Biz

    C->>O: 根据req构建orderdto.Order对象
    C->>B: 调用 ctrl.Biz.NewCreateOrder(ctx, order)
    C->>B: 异步调用 ctrl.Biz.NewCreateStage(order)
    C-->>User: 返回 resp
```


### 节点: (ctrl *OrderController) CostModelApportionConf
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 创建一个成本模型分摊配置的工单。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.CostModelApportionConfReq`: gRPC请求体。
- **输出说明**:
    - `resp *orderproto.CostModelApportionConfResp`: gRPC响应体。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as CostModelApportionConf
    participant O as orderdto.Order
    participant B as ctrl.Biz

    C->>O: 根据req构建orderdto.Order对象
    C->>B: 调用 ctrl.Biz.NewCreateOrder(ctx, order)
    C->>B: 异步调用 ctrl.Biz.NewCreateStage(order)
    C-->>User: 返回 resp
```


### 节点: (ctrl *OrderController) NewModelChildNode
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 创建一个新的模型子节点工单。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.NewModelChildNodeReq`: gRPC请求体。
- **输出说明**:
    - `resp *orderproto.NewModelChildNodeResp`: gRPC响应体。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as NewModelChildNode
    participant O as orderdto.Order
    participant B as ctrl.Biz

    C->>O: 根据req构建orderdto.Order对象
    C->>B: 调用 ctrl.Biz.NewCreateOrder(ctx, order)
    C->>B: 异步调用 ctrl.Biz.NewCreateStage(order)
    C-->>User: 返回 resp
```


### 节点: (ctrl *OrderController) DomainOrder
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 创建一个域名申请的工单。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.DomainOrderReq`: gRPC请求体，包含域名、购买时长、业务树等信息。
- **输出说明**:
    - `resp *orderproto.DomainOrderResp`: gRPC响应体。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as DomainOrder
    participant Cmdb as cmdbbiz
    participant O as orderdto.Order
    participant B as ctrl.Biz

    C->>Cmdb: GetCmdbTreeModelNodeInfos(ctx, ...)
    Note right of C: 查询CMDB获取业务树信息
    C->>O: 根据req和CMDB信息构建orderdto.Order对象
    C->>B: 调用 ctrl.Biz.NewCreateOrder(ctx, order)
    C->>B: 异步调用 ctrl.Biz.NewCreateStage(order)
    C-->>User: 返回 resp
```


### 节点: (ctrl *OrderController) BuyCloudServer
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 创建一个云服务器购买的工单。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.BuyCloudServerReq`: gRPC请求体。
- **输出说明**:
    - `resp *orderproto.BuyCloudServerResp`: gRPC响应体。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as BuyCloudServer
    participant O as orderdto.Order
    participant B as ctrl.Biz

    C->>O: 根据req构建orderdto.Order对象
    C->>B: 调用 ctrl.Biz.NewCreateOrder(ctx, order)
    C->>B: 异步调用 ctrl.Biz.NewCreateStage(order)
    C-->>User: 返回 resp
```


### 节点: (ctrl *OrderController) BuyCloudMysql
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 创建一个云数据库MySQL购买的工单。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.BuyCloudMysqlReq`: gRPC请求体。
- **输出说明**:
    - `resp *orderproto.BuyCloudMysqlResp`: gRPC响应体。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as BuyCloudMysql
    participant O as orderdto.Order
    participant B as ctrl.Biz

    C->>O: 根据req构建orderdto.Order对象
    C->>B: 调用 ctrl.Biz.NewCreateOrder(ctx, order)
    C->>B: 异步调用 ctrl.Biz.NewCreateStage(order)
    C-->>User: 返回 resp
```


### 节点: (ctrl *OrderController) BuyCloudRedis
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 创建一个云数据库Redis购买的工单。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.BuyCloudRedisReq`: gRPC请求体。
- **输出说明**:
    - `resp *orderproto.BuyCloudRedisResp`: gRPC响应体。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as BuyCloudRedis
    participant O as orderdto.Order
    participant B as ctrl.Biz

    C->>O: 根据req构建orderdto.Order对象
    C->>B: 调用 ctrl.Biz.NewCreateOrder(ctx, order)
    C->>B: 异步调用 ctrl.Biz.NewCreateStage(order)
    C-->>User: 返回 resp
```


### 节点: (ctrl *OrderController) CheckResouceDelete
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 校验待删除的资源是否满足删除条件。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.ResouceDeleteReq`: gRPC请求体，包含待删除资源的信息。
- **输出说明**:
    - `err error`: 如果校验失败，则返回错误对象。
- **实现流程**:

```mermaid
flowchart TD
    A[开始] --> B{调用 hook.CheckDeletingResourceHandle2};
    B --> C[返回结果];
```


### 节点: (ctrl *OrderController) ResouceDelete
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 创建一个资源销毁的工单。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.ResouceDeleteReq`: gRPC请求体。
- **输出说明**:
    - `resp *orderproto.ResouceDeleteResp`: gRPC响应体。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as ResouceDelete
    participant Chk as ctrl.CheckResouceDelete
    participant O as orderdto.Order
    participant B as ctrl.Biz

    C->>Chk: CheckResouceDelete(ctx, req)
    C->>O: 根据req构建orderdto.Order对象
    C->>B: 调用 ctrl.Biz.NewCreateOrder(ctx, order)
    C->>B: 异步调用 ctrl.Biz.NewCreateStage(order)
    C-->>User: 返回 resp
```


### 节点: (ctrl *OrderController) UpdateCMDBResourceServiceOwner
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 创建一个更新CMDB资源负责人的工单。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.UpdateCMDBResourceServiceOwnerReq`: gRPC请求体。
- **输出说明**:
    - `resp *orderproto.UpdateCMDBResourceServiceOwnerResp`: gRPC响应体。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as UpdateCMDBResourceServiceOwner
    participant O as orderdto.Order
    participant B as ctrl.Biz

    C->>O: 根据req构建orderdto.Order对象
    C->>B: 调用 ctrl.Biz.NewCreateOrder(ctx, order)
    C->>B: 异步调用 ctrl.Biz.NewCreateStage(order)
    C-->>User: 返回 resp
```


### 节点: (ctrl *OrderController) TokenApply
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 创建一个k8s token申请的工单。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.TokenApplyReq`: gRPC请求体。
- **输出说明**:
    - `resp *orderproto.TokenApplyResp`: gRPC响应体。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as TokenApply
    participant O as orderdto.Order
    participant B as ctrl.Biz

    C->>O: 根据req构建orderdto.Order对象
    C->>B: 调用 ctrl.Biz.NewCreateOrder(ctx, order)
    C->>B: 异步调用 ctrl.Biz.NewCreateStage(order)
    C-->>User: 返回 resp
```


### 节点: (ctrl *OrderController) ModifyModelNode
- **所在代码文件相对路径**: `app/ops/bpm/plugins/order/order.go`
- **用途**: 创建一个修改模型节点的工单。
- **输入参数**:
    - `ctx context.Context`: 请求上下文。
    - `req *orderproto.ModifyModelNodeReq`: gRPC请求体。
- **输出说明**:
    - `resp *orderproto.ModifyModelNodeResp`: gRPC响应体。
    - `err error`: 如果发生错误，则返回错误对象。
- **实现流程**:

```mermaid
sequenceDiagram
    participant C as ModifyModelNode
    participant O as orderdto.Order
    participant B as ctrl.Biz

    C->>O: 根据req构建orderdto.Order对象
    C->>B: 调用 ctrl.Biz.NewCreateOrder(ctx, order)
    C->>B: 异步调用 ctrl.Biz.NewCreateStage(order)
    C-->>User: 返回 resp
```


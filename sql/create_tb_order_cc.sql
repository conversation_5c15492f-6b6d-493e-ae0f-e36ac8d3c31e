-- 创建工单抄送表
CREATE TABLE `tb_order_cc` (
  `id` BIGINT NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `order_id` VARCHAR(16) NOT NULL COMMENT '工单ID',
  `cc_open_id` VARCHAR(64) NOT NULL COMMENT '被抄送人飞书Open ID',
  `cc_email` VARCHAR(32) NOT NULL COMMENT '被抄送人邮箱',
  `ctime` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `mtime` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_cc` (`order_id`, `cc_open_id`) COMMENT '工单ID和抄送人OpenID联合唯一索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='工单抄送表';
